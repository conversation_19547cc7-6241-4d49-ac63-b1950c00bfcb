#include "gameobject.h"
#include <QtMath>
#include <QOpenGLFunctions>

GameObject::GameObject()
    : m_position(0.0f, 0.0f, 0.0f)
    , m_rotation(0.0f, 0.0f, 0.0f)
    , m_scale(1.0f, 1.0f, 1.0f)
    , m_color(1.0f, 1.0f, 1.0f)
    , m_vbo(QOpenGLBuffer::VertexBuffer)
    , m_ebo(QOpenGLBuffer::IndexBuffer)
    , m_initialized(false)
    , m_vertexCount(0)
    , m_indexCount(0)
{
}

GameObject::~GameObject()
{
    cleanup();
}

void GameObject::setPosition(const QVector3D &position)
{
    m_position = position;
}

void GameObject::setRotation(const QVector3D &rotation)
{
    m_rotation = rotation;
}

void GameObject::setScale(const QVector3D &scale)
{
    m_scale = scale;
}

void GameObject::setScale(float uniformScale)
{
    m_scale = QVector3D(uniformScale, uniformScale, uniformScale);
}

void GameObject::translate(const QVector3D &translation)
{
    m_position += translation;
}

void GameObject::rotate(const QVector3D &rotation)
{
    m_rotation += rotation;
}

void GameObject::scaleBy(float factor)
{
    m_scale *= factor;
}

QMatrix4x4 GameObject::modelMatrix() const
{
    QMatrix4x4 model;
    model.translate(m_position);
    model.rotate(m_rotation.x(), QVector3D(1, 0, 0));
    model.rotate(m_rotation.y(), QVector3D(0, 1, 0));
    model.rotate(m_rotation.z(), QVector3D(0, 0, 1));
    model.scale(m_scale);
    return model;
}

void GameObject::setVertices(const QVector<float> &vertices)
{
    m_vertices = vertices;
    m_vertexCount = vertices.size() / 6; // Assuming position + normal (3 + 3 floats per vertex)
}

void GameObject::setIndices(const QVector<unsigned int> &indices)
{
    m_indices = indices;
    m_indexCount = indices.size();
}

void GameObject::setColor(const QVector3D &color)
{
    m_color = color;
}

void GameObject::setupMesh()
{
    if (m_vertices.isEmpty()) {
        return;
    }

    // Create VAO
    m_vao.create();
    m_vao.bind();

    // Create and setup VBO
    m_vbo.create();
    m_vbo.bind();
    m_vbo.allocate(m_vertices.constData(), m_vertices.size() * sizeof(float));

    // Position attribute (location 0)
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)0);

    // Normal attribute (location 1)
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)(3 * sizeof(float)));

    // Setup EBO if indices are provided
    if (!m_indices.isEmpty()) {
        m_ebo.create();
        m_ebo.bind();
        m_ebo.allocate(m_indices.constData(), m_indices.size() * sizeof(unsigned int));
    }

    m_vao.release();
    m_initialized = true;
}

void GameObject::render()
{
    if (!m_initialized) {
        return;
    }

    m_vao.bind();
    
    if (m_indexCount > 0) {
        glDrawElements(GL_TRIANGLES, m_indexCount, GL_UNSIGNED_INT, 0);
    } else {
        glDrawArrays(GL_TRIANGLES, 0, m_vertexCount);
    }
    
    m_vao.release();
}

void GameObject::cleanup()
{
    if (m_initialized) {
        m_vao.destroy();
        m_vbo.destroy();
        m_ebo.destroy();
        m_initialized = false;
    }
}

GameObject* GameObject::createCube(float size)
{
    GameObject* cube = new GameObject();
    cube->generateCubeVertices(size);
    return cube;
}

GameObject* GameObject::createSphere(float radius, int segments)
{
    GameObject* sphere = new GameObject();
    sphere->generateSphereVertices(radius, segments);
    return sphere;
}

GameObject* GameObject::createPlane(float width, float height)
{
    GameObject* plane = new GameObject();
    plane->generatePlaneVertices(width, height);
    return plane;
}

void GameObject::generateCubeVertices(float size)
{
    float s = size / 2.0f;

    // Cube vertices with positions and normals
    QVector<float> vertices = {
        // Front face
        -s, -s,  s,  0.0f,  0.0f,  1.0f,
         s, -s,  s,  0.0f,  0.0f,  1.0f,
         s,  s,  s,  0.0f,  0.0f,  1.0f,
        -s,  s,  s,  0.0f,  0.0f,  1.0f,

        // Back face
        -s, -s, -s,  0.0f,  0.0f, -1.0f,
        -s,  s, -s,  0.0f,  0.0f, -1.0f,
         s,  s, -s,  0.0f,  0.0f, -1.0f,
         s, -s, -s,  0.0f,  0.0f, -1.0f,

        // Left face
        -s,  s,  s, -1.0f,  0.0f,  0.0f,
        -s,  s, -s, -1.0f,  0.0f,  0.0f,
        -s, -s, -s, -1.0f,  0.0f,  0.0f,
        -s, -s,  s, -1.0f,  0.0f,  0.0f,

        // Right face
         s,  s,  s,  1.0f,  0.0f,  0.0f,
         s, -s,  s,  1.0f,  0.0f,  0.0f,
         s, -s, -s,  1.0f,  0.0f,  0.0f,
         s,  s, -s,  1.0f,  0.0f,  0.0f,

        // Bottom face
        -s, -s, -s,  0.0f, -1.0f,  0.0f,
         s, -s, -s,  0.0f, -1.0f,  0.0f,
         s, -s,  s,  0.0f, -1.0f,  0.0f,
        -s, -s,  s,  0.0f, -1.0f,  0.0f,

        // Top face
        -s,  s, -s,  0.0f,  1.0f,  0.0f,
        -s,  s,  s,  0.0f,  1.0f,  0.0f,
         s,  s,  s,  0.0f,  1.0f,  0.0f,
         s,  s, -s,  0.0f,  1.0f,  0.0f
    };

    QVector<unsigned int> indices = {
        0,  1,  2,   2,  3,  0,   // Front
        4,  5,  6,   6,  7,  4,   // Back
        8,  9, 10,  10, 11,  8,   // Left
       12, 13, 14,  14, 15, 12,   // Right
       16, 17, 18,  18, 19, 16,   // Bottom
       20, 21, 22,  22, 23, 20    // Top
    };

    setVertices(vertices);
    setIndices(indices);
}

void GameObject::generateSphereVertices(float radius, int segments)
{
    QVector<float> vertices;
    QVector<unsigned int> indices;

    // Generate vertices
    for (int i = 0; i <= segments; ++i) {
        float phi = M_PI * float(i) / float(segments);
        for (int j = 0; j <= segments; ++j) {
            float theta = 2.0f * M_PI * float(j) / float(segments);

            float x = radius * qSin(phi) * qCos(theta);
            float y = radius * qCos(phi);
            float z = radius * qSin(phi) * qSin(theta);

            // Position
            vertices.append(x);
            vertices.append(y);
            vertices.append(z);

            // Normal (normalized position for sphere)
            vertices.append(x / radius);
            vertices.append(y / radius);
            vertices.append(z / radius);
        }
    }

    // Generate indices
    for (int i = 0; i < segments; ++i) {
        for (int j = 0; j < segments; ++j) {
            int first = i * (segments + 1) + j;
            int second = first + segments + 1;

            indices.append(first);
            indices.append(second);
            indices.append(first + 1);

            indices.append(second);
            indices.append(second + 1);
            indices.append(first + 1);
        }
    }

    setVertices(vertices);
    setIndices(indices);
}

void GameObject::generatePlaneVertices(float width, float height)
{
    float w = width / 2.0f;
    float h = height / 2.0f;

    QVector<float> vertices = {
        // Position        // Normal
        -w, 0.0f, -h,      0.0f, 1.0f, 0.0f,
         w, 0.0f, -h,      0.0f, 1.0f, 0.0f,
         w, 0.0f,  h,      0.0f, 1.0f, 0.0f,
        -w, 0.0f,  h,      0.0f, 1.0f, 0.0f
    };

    QVector<unsigned int> indices = {
        0, 1, 2,
        2, 3, 0
    };

    setVertices(vertices);
    setIndices(indices);
}
