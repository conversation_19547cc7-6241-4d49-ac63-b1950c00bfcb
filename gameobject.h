#ifndef GAMEOBJECT_H
#define GAMEOBJECT_H

#include <QVector3D>
#include <QMatrix4x4>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>

/**
 * @brief The GameObject class represents a 3D object in the game world
 * 
 * This class handles the transformation, rendering data, and basic
 * properties of 3D objects in the scene.
 */
class GameObject
{
public:
    GameObject();
    ~GameObject();
    
    // Transformation
    void setPosition(const QVector3D &position);
    void setRotation(const QVector3D &rotation);
    void setScale(const QVector3D &scale);
    void setScale(float uniformScale);
    
    QVector3D position() const { return m_position; }
    QVector3D rotation() const { return m_rotation; }
    QVector3D scale() const { return m_scale; }
    
    // Transform operations
    void translate(const QVector3D &translation);
    void rotate(const QVector3D &rotation);
    void scaleBy(float factor);
    
    // Matrix calculations
    QMatrix4x4 modelMatrix() const;
    
    // Mesh data
    void setVertices(const QVector<float> &vertices);
    void setIndices(const QVector<unsigned int> &indices);
    void setColor(const QVector3D &color);
    QVector3D color() const { return m_color; }
    
    // OpenGL setup
    void setupMesh();
    void render();
    void cleanup();
    
    // Utility functions
    bool isInitialized() const { return m_initialized; }
    int vertexCount() const { return m_vertexCount; }
    int indexCount() const { return m_indexCount; }
    
    // Predefined shapes
    static GameObject* createCube(float size = 1.0f);
    static GameObject* createSphere(float radius = 1.0f, int segments = 16);
    static GameObject* createPlane(float width = 1.0f, float height = 1.0f);

private:
    // Transformation properties
    QVector3D m_position;
    QVector3D m_rotation;
    QVector3D m_scale;
    QVector3D m_color;
    
    // Mesh data
    QVector<float> m_vertices;
    QVector<unsigned int> m_indices;
    
    // OpenGL objects
    QOpenGLVertexArrayObject m_vao;
    QOpenGLBuffer m_vbo;
    QOpenGLBuffer m_ebo;
    
    // State
    bool m_initialized;
    int m_vertexCount;
    int m_indexCount;
    
    // Helper functions
    void generateSphereVertices(float radius, int segments);
    void generateCubeVertices(float size);
    void generatePlaneVertices(float width, float height);
};

#endif // GAMEOBJECT_H
