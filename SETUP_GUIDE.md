# Simple 3D Game - Complete Setup Guide

This guide will walk you through setting up and running the Simple 3D Game project in Qt Creator.

## Prerequisites

### 1. Install Qt and Qt Creator

**Option A: Qt Online Installer (Recommended)**
1. Download Qt Online Installer from [qt.io/download](https://www.qt.io/download)
2. Run the installer and create a Qt account (free)
3. Select the following components:
   - Qt 6.5 or later (latest LTS recommended)
   - Qt Creator IDE
   - MinGW compiler (Windows) or appropriate compiler for your platform
   - Qt OpenGL and OpenGL Widgets modules

**Option B: Qt Offline Installer**
1. Download the appropriate Qt installer for your platform
2. Ensure you include Qt Creator and OpenGL modules

### 2. Verify OpenGL Support
- Ensure your graphics drivers are up to date
- Your system should support OpenGL 3.3 or later
- Most modern systems (2010+) support this requirement

## Project Setup in Qt Creator

### Method 1: Using the .pro file (Recommended)

1. **Open Qt Creator**
2. **Open Project**:
   - File → Open File or Project
   - Navigate to your project folder
   - Select `Simple3DGame.pro`
   - Click "Open"

3. **Configure Project**:
   - Qt Creator will show the "Configure Project" screen
   - Select your Qt kit (usually auto-detected)
   - Click "Configure Project"

4. **Build and Run**:
   - Click the green "Run" button (▶) or press Ctrl+R
   - Qt Creator will automatically build and run the project

### Method 2: Using CMake

1. **Open Qt Creator**
2. **Open Project**:
   - File → Open File or Project
   - Navigate to your project folder
   - Select `CMakeLists.txt`
   - Click "Open"

3. **Configure CMake**:
   - Qt Creator will detect CMake configuration
   - Select your Qt kit
   - Click "Configure Project"

4. **Build and Run**:
   - Click the green "Run" button (▶) or press Ctrl+R

## Manual Build (Command Line)

### Using qmake (if available in PATH)
```bash
qmake Simple3DGame.pro
make  # or mingw32-make on Windows
./Simple3DGame  # or Simple3DGame.exe on Windows
```

### Using CMake
```bash
mkdir build
cd build
cmake ..
cmake --build .
./Simple3DGame  # or Simple3DGame.exe on Windows
```

### Windows Batch Script
Simply double-click `build.bat` (requires CMake and MinGW in PATH)

## Troubleshooting

### Common Issues and Solutions

#### 1. "Qt modules not found"
**Problem**: CMake or qmake cannot find Qt modules
**Solution**: 
- Ensure Qt is properly installed
- Set Qt6_DIR environment variable to Qt installation path
- In Qt Creator, check Tools → Options → Kits

#### 2. "OpenGL functions not available"
**Problem**: OpenGL context creation fails
**Solution**:
- Update graphics drivers
- Check if your system supports OpenGL 3.3+
- Try running on a different machine for testing

#### 3. "Shader compilation failed"
**Problem**: GLSL shaders don't compile
**Solution**:
- Check console output for shader errors
- Verify OpenGL version compatibility
- Ensure graphics drivers are current

#### 4. "Black screen on startup"
**Problem**: Window opens but shows only black screen
**Solution**:
- Check debug output in Qt Creator's "Application Output"
- Verify OpenGL context creation
- Try different OpenGL format settings

#### 5. Build errors about missing headers
**Problem**: Cannot find Qt headers
**Solution**:
- Verify Qt installation includes development headers
- Check kit configuration in Qt Creator
- Ensure all required Qt modules are installed

### Debug Information

The application outputs useful debug information to the console:
- OpenGL version and capabilities
- Shader compilation status
- Initialization progress

To view this in Qt Creator:
1. Run the application in Debug mode
2. Check the "Application Output" panel
3. Look for any error messages or warnings

### Performance Issues

If the application runs slowly:
1. Check if you're using integrated vs. dedicated graphics
2. Reduce window size for testing
3. Verify graphics drivers are current
4. Check system requirements

## Project Structure Overview

```
Simple3DGame/
├── Simple3DGame.pro      # Qt project file (primary)
├── CMakeLists.txt        # CMake build file (alternative)
├── build.bat             # Windows build script
├── main.cpp              # Application entry point
├── mainwindow.h/cpp/ui   # Main window management
├── openglwidget.h/cpp    # OpenGL rendering
├── camera.h/cpp          # 3D camera system
├── gameobject.h/cpp      # 3D object representation
├── gameengine.h/cpp      # Game logic and state
├── README.md             # Project documentation
└── SETUP_GUIDE.md        # This setup guide
```

## First Run Expectations

When you successfully run the project, you should see:

1. **Window**: A 1024x768 window titled "Simple 3D Game - Qt OpenGL"
2. **Scene**: A 3D scene with:
   - Orange cube at the center
   - Green smaller cube to the right
   - Blue sphere to the left
   - Gray ground plane
3. **Controls**: WASD movement, mouse look (click and drag)
4. **UI**: Menu bar with File, View, and Help menus
5. **Status Bar**: FPS counter and camera position

## Next Steps

Once the project is running:

1. **Explore the Controls**: Try moving around with WASD and mouse
2. **Examine the Code**: Look at the well-commented source files
3. **Modify the Scene**: Try changing object positions or colors
4. **Add Features**: Extend the game with your own ideas

## Getting Help

If you encounter issues:

1. Check the console output for error messages
2. Verify all prerequisites are met
3. Try the alternative build method (CMake vs qmake)
4. Ensure Qt Creator is properly configured
5. Test with a minimal Qt OpenGL example first

The project is designed to be educational and extensible. Once running, you'll have a solid foundation for 3D game development with Qt and OpenGL!
