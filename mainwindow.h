#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>

QT_BEGIN_NAMESPACE
class QLabel;
QT_END_NAMESPACE

class OpenGLWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void keyReleaseEvent(QKeyEvent *event) override;

private slots:
    void updateStatusBar();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();

    OpenGLWidget *m_openglWidget;
    QLabel *m_fpsLabel;
    QLabel *m_positionLabel;
    QTimer *m_statusUpdateTimer;
};

#endif // MAINWINDOW_H
