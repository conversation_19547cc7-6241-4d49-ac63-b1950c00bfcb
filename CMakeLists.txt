cmake_minimum_required(VERSION 3.16)

project(Simple3DGame VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets OpenGL OpenGLWidgets)

# Enable automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    main.cpp
    mainwindow.cpp
    openglwidget.cpp
    camera.cpp
    gameobject.cpp
    gameengine.cpp
)

# Header files
set(HEADERS
    mainwindow.h
    openglwidget.h
    camera.h
    gameobject.h
    gameengine.h
)

# UI files
set(UI_FILES
    mainwindow.ui
)

# Create the executable
add_executable(Simple3DGame ${SOURCES} ${HEADERS} ${UI_FILES})

# Link Qt6 libraries
target_link_libraries(Simple3DGame
    Qt6::Core
    Qt6::Widgets
    Qt6::OpenGL
    Qt6::OpenGLWidgets
)

# Platform-specific OpenGL libraries
if(WIN32)
    target_link_libraries(Simple3DGame opengl32)
elseif(UNIX AND NOT APPLE)
    target_link_libraries(Simple3DGame GL GLU)
elseif(APPLE)
    find_library(OPENGL_LIBRARY OpenGL)
    target_link_libraries(Simple3DGame ${OPENGL_LIBRARY})
endif()

# Set target properties
set_target_properties(Simple3DGame PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Copy Qt6 DLLs to output directory on Windows (for development)
if(WIN32 AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_custom_command(TARGET Simple3DGame POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:Qt6::Core>
        $<TARGET_FILE:Qt6::Widgets>
        $<TARGET_FILE:Qt6::OpenGL>
        $<TARGET_FILE:Qt6::OpenGLWidgets>
        $<TARGET_FILE_DIR:Simple3DGame>
    )
endif()
