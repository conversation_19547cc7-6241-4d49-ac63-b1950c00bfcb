#include "openglwidget.h"
#include "gameengine.h"
#include "gameobject.h"
#include "camera.h"
#include <QApplication>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QKeyEvent>
#include <QtMath>
#include <QDebug>

OpenGLWidget::OpenGLWidget(QWidget *parent)
    : QOpenGLWidget(parent)
    , m_gameEngine(nullptr)
    , m_shaderProgram(nullptr)
    , m_modelMatrixLocation(-1)
    , m_viewMatrixLocation(-1)
    , m_projectionMatrixLocation(-1)
    , m_normalMatrixLocation(-1)
    , m_lightPositionLocation(-1)
    , m_lightColorLocation(-1)
    , m_objectColorLocation(-1)
    , m_viewPositionLocation(-1)
    , m_updateTimer(nullptr)
    , m_lastFrameTime(0)
    , m_deltaTime(0.0f)
    , m_mousePressed(false)
    , m_mouseCaptured(false)
    , m_windowWidth(800)
    , m_windowHeight(600)
    , m_fps(0.0f)
    , m_frameCount(0)
    , m_lastFPSUpdate(0)
{
    setFocusPolicy(Qt::StrongFocus);
    setMouseTracking(true);
    
    // Create game engine
    m_gameEngine = std::make_unique<GameEngine>(this);
    
    // Set up update timer for game loop
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &OpenGLWidget::updateFrame);
    m_updateTimer->start(16); // ~60 FPS
    
    m_frameTimer.start();
    m_lastFrameTime = m_frameTimer.elapsed();
    m_lastFPSUpdate = m_lastFrameTime;
}

OpenGLWidget::~OpenGLWidget()
{
    makeCurrent();
    // Cleanup is handled automatically by Qt
    doneCurrent();
}

void OpenGLWidget::initializeGL()
{
    initializeOpenGLFunctions();
    
    // Set clear color and enable depth testing
    glClearColor(0.1f, 0.1f, 0.2f, 1.0f);
    glEnable(GL_DEPTH_TEST);
    glEnable(GL_CULL_FACE);
    glCullFace(GL_BACK);
    
    // Setup shaders
    setupShaders();
    
    // Setup game engine and scene
    m_gameEngine->setupDefaultScene();
    
    // Initialize all game objects
    for (auto& object : m_gameEngine->gameObjects()) {
        if (object) {
            object->setupMesh();
        }
    }
    
    // Start the game engine
    m_gameEngine->start();
    
    qDebug() << "OpenGL initialized successfully";
    qDebug() << "OpenGL Version:" << (char*)glGetString(GL_VERSION);
    qDebug() << "GLSL Version:" << (char*)glGetString(GL_SHADING_LANGUAGE_VERSION);
}

void OpenGLWidget::resizeGL(int w, int h)
{
    m_windowWidth = w;
    m_windowHeight = h;
    
    glViewport(0, 0, w, h);
    
    // Update projection matrix
    m_projectionMatrix.setToIdentity();
    float aspect = float(w) / float(h);
    m_projectionMatrix.perspective(45.0f, aspect, 0.1f, 100.0f);
}

void OpenGLWidget::paintGL()
{
    // Clear buffers
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    if (!m_shaderProgram || !m_gameEngine) {
        return;
    }
    
    // Use shader program
    m_shaderProgram->bind();
    
    // Setup matrices
    setupMatrices();
    
    // Render the scene
    renderScene();
    
    m_shaderProgram->release();
    
    // Update frame count for FPS calculation
    m_frameCount++;
    qint64 currentTime = m_frameTimer.elapsed();
    if (currentTime - m_lastFPSUpdate >= 1000) {
        m_fps = m_frameCount * 1000.0f / (currentTime - m_lastFPSUpdate);
        m_frameCount = 0;
        m_lastFPSUpdate = currentTime;
    }
}

void OpenGLWidget::updateFrame()
{
    // Calculate delta time
    qint64 currentTime = m_frameTimer.elapsed();
    m_deltaTime = (currentTime - m_lastFrameTime) / 1000.0f;
    m_lastFrameTime = currentTime;
    
    // Update game engine
    if (m_gameEngine) {
        m_gameEngine->update(m_deltaTime);
    }
    
    // Trigger repaint
    update();
}

void OpenGLWidget::handleKeyPress(QKeyEvent *event)
{
    if (m_gameEngine) {
        m_gameEngine->handleKeyPress(event->key());
    }
}

void OpenGLWidget::handleKeyRelease(QKeyEvent *event)
{
    if (m_gameEngine) {
        m_gameEngine->handleKeyRelease(event->key());
    }
}

QVector3D OpenGLWidget::getCameraPosition() const
{
    if (m_gameEngine && m_gameEngine->camera()) {
        return m_gameEngine->camera()->position();
    }
    return QVector3D(0, 0, 0);
}

void OpenGLWidget::resetCamera()
{
    if (m_gameEngine && m_gameEngine->camera()) {
        m_gameEngine->camera()->reset();
    }
}

float OpenGLWidget::getFPS() const
{
    return m_fps;
}

void OpenGLWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_mousePressed = true;
        m_lastMousePosition = event->pos();
        m_mouseCaptured = true;
        setCursor(Qt::BlankCursor);
    }
    QOpenGLWidget::mousePressEvent(event);
}

void OpenGLWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_mouseCaptured && m_gameEngine) {
        QPoint delta = event->pos() - m_lastMousePosition;
        m_gameEngine->handleMouseMove(delta.x(), delta.y());

        // Keep mouse in center of widget
        QPoint center = rect().center();
        QCursor::setPos(mapToGlobal(center));
        m_lastMousePosition = center;
    }
    QOpenGLWidget::mouseMoveEvent(event);
}

void OpenGLWidget::wheelEvent(QWheelEvent *event)
{
    // Handle mouse wheel for camera movement
    if (m_gameEngine && m_gameEngine->camera()) {
        float delta = event->angleDelta().y() / 120.0f; // Standard wheel step
        if (delta > 0) {
            m_gameEngine->camera()->moveForward(0.5f);
        } else {
            m_gameEngine->camera()->moveBackward(0.5f);
        }
    }
    QOpenGLWidget::wheelEvent(event);
}

void OpenGLWidget::focusInEvent(QFocusEvent *event)
{
    QOpenGLWidget::focusInEvent(event);
}

void OpenGLWidget::focusOutEvent(QFocusEvent *event)
{
    // Release mouse capture when losing focus
    m_mouseCaptured = false;
    m_mousePressed = false;
    setCursor(Qt::ArrowCursor);
    QOpenGLWidget::focusOutEvent(event);
}

void OpenGLWidget::setupShaders()
{
    m_shaderProgram = std::make_unique<QOpenGLShaderProgram>();

    // Vertex shader
    const char* vertexShaderSource = R"(
        #version 330 core
        layout (location = 0) in vec3 aPos;
        layout (location = 1) in vec3 aNormal;

        uniform mat4 model;
        uniform mat4 view;
        uniform mat4 projection;
        uniform mat3 normalMatrix;

        out vec3 FragPos;
        out vec3 Normal;

        void main()
        {
            FragPos = vec3(model * vec4(aPos, 1.0));
            Normal = normalMatrix * aNormal;

            gl_Position = projection * view * vec4(FragPos, 1.0);
        }
    )";

    // Fragment shader
    const char* fragmentShaderSource = R"(
        #version 330 core
        out vec4 FragColor;

        in vec3 FragPos;
        in vec3 Normal;

        uniform vec3 lightPos;
        uniform vec3 lightColor;
        uniform vec3 objectColor;
        uniform vec3 viewPos;

        void main()
        {
            // Ambient
            float ambientStrength = 0.1;
            vec3 ambient = ambientStrength * lightColor;

            // Diffuse
            vec3 norm = normalize(Normal);
            vec3 lightDir = normalize(lightPos - FragPos);
            float diff = max(dot(norm, lightDir), 0.0);
            vec3 diffuse = diff * lightColor;

            // Specular
            float specularStrength = 0.5;
            vec3 viewDir = normalize(viewPos - FragPos);
            vec3 reflectDir = reflect(-lightDir, norm);
            float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32);
            vec3 specular = specularStrength * spec * lightColor;

            vec3 result = (ambient + diffuse + specular) * objectColor;
            FragColor = vec4(result, 1.0);
        }
    )";

    // Compile shaders
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        qDebug() << "Vertex shader compilation failed:" << m_shaderProgram->log();
        return;
    }

    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        qDebug() << "Fragment shader compilation failed:" << m_shaderProgram->log();
        return;
    }

    if (!m_shaderProgram->link()) {
        qDebug() << "Shader program linking failed:" << m_shaderProgram->log();
        return;
    }

    // Get uniform locations
    m_modelMatrixLocation = m_shaderProgram->uniformLocation("model");
    m_viewMatrixLocation = m_shaderProgram->uniformLocation("view");
    m_projectionMatrixLocation = m_shaderProgram->uniformLocation("projection");
    m_normalMatrixLocation = m_shaderProgram->uniformLocation("normalMatrix");
    m_lightPositionLocation = m_shaderProgram->uniformLocation("lightPos");
    m_lightColorLocation = m_shaderProgram->uniformLocation("lightColor");
    m_objectColorLocation = m_shaderProgram->uniformLocation("objectColor");
    m_viewPositionLocation = m_shaderProgram->uniformLocation("viewPos");

    qDebug() << "Shaders compiled and linked successfully";
}

void OpenGLWidget::setupMatrices()
{
    if (!m_gameEngine || !m_gameEngine->camera()) {
        return;
    }

    // Get view matrix from camera
    m_viewMatrix = m_gameEngine->camera()->viewMatrix();

    // Set matrices in shader
    m_shaderProgram->setUniformValue(m_viewMatrixLocation, m_viewMatrix);
    m_shaderProgram->setUniformValue(m_projectionMatrixLocation, m_projectionMatrix);

    // Set lighting uniforms
    const auto& light = m_gameEngine->light();
    m_shaderProgram->setUniformValue(m_lightPositionLocation, light.position);
    m_shaderProgram->setUniformValue(m_lightColorLocation, light.color);
    m_shaderProgram->setUniformValue(m_viewPositionLocation, m_gameEngine->camera()->position());
}

void OpenGLWidget::renderScene()
{
    if (!m_gameEngine) {
        return;
    }

    // Render all game objects
    for (const auto& object : m_gameEngine->gameObjects()) {
        if (object && object->isInitialized()) {
            renderGameObject(object);
        }
    }
}

void OpenGLWidget::renderGameObject(const std::shared_ptr<GameObject> &object)
{
    if (!object || !m_shaderProgram) {
        return;
    }

    // Set model matrix
    QMatrix4x4 modelMatrix = object->modelMatrix();
    m_shaderProgram->setUniformValue(m_modelMatrixLocation, modelMatrix);

    // Calculate and set normal matrix
    QMatrix3x3 normalMatrix = modelMatrix.normalMatrix();
    m_shaderProgram->setUniformValue(m_normalMatrixLocation, normalMatrix);

    // Set object color
    QVector3D objectColor = object->color();
    m_shaderProgram->setUniformValue(m_objectColorLocation, objectColor);

    // Render the object
    object->render();
}
