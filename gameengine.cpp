#include "gameengine.h"
#include "gameobject.h"
#include "camera.h"
#include <Qt>
#include <QtMath>

GameEngine::GameEngine(QObject *parent)
    : QObject(parent)
    , m_running(false)
    , m_paused(false)
    , m_deltaTime(0.0f)
    , m_totalTime(0.0f)
    , m_frameCount(0)
    , m_fps(0.0f)
    , m_lastFrameTime(0)
    , m_mouseSensitivity(0.1f)
    , m_movementSpeed(5.0f)
{
    m_timer.start();
}

GameEngine::~GameEngine()
{
    clearGameObjects();
}

void GameEngine::update(float deltaTime)
{
    if (!m_running || m_paused) {
        return;
    }
    
    m_deltaTime = deltaTime;
    m_totalTime += deltaTime;
    m_frameCount++;
    
    updateInput(deltaTime);
    updateGameObjects(deltaTime);
    calculateFPS();
}

void GameEngine::render()
{
    // Rendering is handled by the OpenGL widget
    // This method can be used for game-specific rendering logic
}

void GameEngine::addGameObject(std::shared_ptr<GameObject> object)
{
    if (object) {
        m_gameObjects.append(object);
    }
}

void GameEngine::removeGameObject(std::shared_ptr<GameObject> object)
{
    m_gameObjects.removeAll(object);
}

void GameEngine::clearGameObjects()
{
    m_gameObjects.clear();
}

void GameEngine::setCamera(std::shared_ptr<Camera> camera)
{
    m_camera = camera;
}

void GameEngine::handleKeyPress(int key)
{
    m_pressedKeys.insert(key);
}

void GameEngine::handleKeyRelease(int key)
{
    m_pressedKeys.remove(key);
}

void GameEngine::handleMouseMove(float deltaX, float deltaY)
{
    if (m_camera) {
        m_camera->rotate(deltaX, -deltaY); // Invert Y for natural mouse look
    }
}

void GameEngine::start()
{
    m_running = true;
    m_paused = false;
    m_timer.restart();
    m_lastFrameTime = m_timer.elapsed();
}

void GameEngine::pause()
{
    m_paused = true;
}

void GameEngine::resume()
{
    m_paused = false;
    m_lastFrameTime = m_timer.elapsed();
}

void GameEngine::stop()
{
    m_running = false;
    m_paused = false;
}

void GameEngine::setupDefaultScene()
{
    // Create a camera
    m_camera = std::make_shared<Camera>();
    m_camera->setPosition(QVector3D(0.0f, 2.0f, 5.0f));
    
    // Create some default objects
    auto cube1 = std::shared_ptr<GameObject>(GameObject::createCube(1.0f));
    cube1->setPosition(QVector3D(0.0f, 0.0f, 0.0f));
    cube1->setColor(QVector3D(1.0f, 0.5f, 0.2f)); // Orange
    addGameObject(cube1);
    
    auto cube2 = std::shared_ptr<GameObject>(GameObject::createCube(0.5f));
    cube2->setPosition(QVector3D(2.0f, 0.0f, -1.0f));
    cube2->setColor(QVector3D(0.2f, 0.8f, 0.2f)); // Green
    addGameObject(cube2);
    
    auto sphere = std::shared_ptr<GameObject>(GameObject::createSphere(0.8f, 16));
    sphere->setPosition(QVector3D(-2.0f, 1.0f, 0.0f));
    sphere->setColor(QVector3D(0.2f, 0.5f, 1.0f)); // Blue
    addGameObject(sphere);
    
    auto ground = std::shared_ptr<GameObject>(GameObject::createPlane(10.0f, 10.0f));
    ground->setPosition(QVector3D(0.0f, -1.0f, 0.0f));
    ground->setColor(QVector3D(0.8f, 0.8f, 0.8f)); // Gray
    addGameObject(ground);
    
    // Set up lighting
    m_light = Light(QVector3D(3.0f, 5.0f, 3.0f), QVector3D(1.0f, 1.0f, 1.0f), 1.0f);
}

void GameEngine::updateInput(float deltaTime)
{
    if (!m_camera) {
        return;
    }
    
    // Camera movement based on pressed keys
    if (m_pressedKeys.contains(Qt::Key_W)) {
        m_camera->moveForward(deltaTime);
    }
    if (m_pressedKeys.contains(Qt::Key_S)) {
        m_camera->moveBackward(deltaTime);
    }
    if (m_pressedKeys.contains(Qt::Key_A)) {
        m_camera->moveLeft(deltaTime);
    }
    if (m_pressedKeys.contains(Qt::Key_D)) {
        m_camera->moveRight(deltaTime);
    }
    if (m_pressedKeys.contains(Qt::Key_Space)) {
        m_camera->moveUp(deltaTime);
    }
    if (m_pressedKeys.contains(Qt::Key_Shift)) {
        m_camera->moveDown(deltaTime);
    }
}

void GameEngine::updateGameObjects(float deltaTime)
{
    // Update all game objects
    for (auto& object : m_gameObjects) {
        if (object) {
            // Add some rotation to make the scene more dynamic
            QVector3D rotation = object->rotation();
            rotation.setY(rotation.y() + 30.0f * deltaTime); // Rotate 30 degrees per second
            object->setRotation(rotation);
        }
    }
}

void GameEngine::calculateFPS()
{
    static int frameCounter = 0;
    static qint64 lastFPSUpdate = 0;
    
    frameCounter++;
    qint64 currentTime = m_timer.elapsed();
    
    if (currentTime - lastFPSUpdate >= 1000) { // Update FPS every second
        m_fps = frameCounter * 1000.0f / (currentTime - lastFPSUpdate);
        frameCounter = 0;
        lastFPSUpdate = currentTime;
    }
}
