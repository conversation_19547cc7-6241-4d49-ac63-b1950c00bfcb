# Simple 3D Game - Qt OpenGL

A complete 3D game foundation built with Qt Creator and OpenGL, providing a solid base for developing more complex 3D games.

## Features

- **3D Rendering**: Modern OpenGL 3.3 Core Profile with custom shaders
- **Camera System**: First-person camera with WASD movement and mouse look
- **Game Objects**: Modular GameObject system with built-in shapes (cube, sphere, plane)
- **Lighting**: Phong lighting model with ambient, diffuse, and specular components
- **Game Loop**: Frame-based rendering with delta time calculations
- **Input Handling**: Keyboard and mouse input processing
- **Performance Monitoring**: Real-time FPS display and camera position tracking

## Requirements

### Software Dependencies
- **Qt 6.0 or later** (with OpenGL support)
- **Qt Creator IDE** (recommended)
- **C++17 compatible compiler**
- **OpenGL 3.3 or later** support

### Qt Modules Required
- Qt Core
- Qt Widgets
- Qt OpenGL
- Qt OpenGL Widgets

## Setup Instructions

### 1. Install Qt and Qt Creator
- Download and install Qt from [qt.io](https://www.qt.io/download)
- Make sure to include Qt Creator IDE and the OpenGL modules
- Ensure your graphics drivers support OpenGL 3.3 or later

### 2. Open the Project
1. Launch Qt Creator
2. Open the project file: `Simple3DGame.pro`
3. Configure the project with your Qt kit (usually auto-detected)

### 3. Build and Run
1. Click the "Build" button (Ctrl+B) or use Build menu
2. Click the "Run" button (Ctrl+R) or use Build → Run menu
3. The game window should open with a 3D scene

## Controls

- **WASD**: Move camera forward/backward/left/right
- **Space**: Move camera up
- **Shift**: Move camera down
- **Mouse**: Look around (click and drag)
- **Mouse Wheel**: Move forward/backward
- **R**: Reset camera to default position
- **ESC**: Exit the application

## Project Structure

```
Simple3DGame/
├── main.cpp              # Application entry point
├── mainwindow.h/cpp      # Main window and UI management
├── openglwidget.h/cpp    # OpenGL rendering and input handling
├── camera.h/cpp          # 3D camera implementation
├── gameobject.h/cpp      # 3D object representation
├── gameengine.h/cpp      # Game logic and state management
├── mainwindow.ui         # UI layout file
├── Simple3DGame.pro      # Qt project file
└── README.md             # This file
```

## Architecture Overview

### Core Components

1. **MainWindow**: Manages the application window, menus, and status bar
2. **OpenGLWidget**: Handles OpenGL context, rendering, and input events
3. **GameEngine**: Manages game state, timing, and object updates
4. **Camera**: Provides first-person camera functionality
5. **GameObject**: Represents 3D objects with transformation and rendering data

### Rendering Pipeline

1. **Initialization**: Set up OpenGL context, compile shaders, create game objects
2. **Game Loop**: Update game state, process input, calculate transformations
3. **Rendering**: Clear buffers, set uniforms, render all game objects
4. **Present**: Swap buffers and display the frame

### Shader System

The project uses custom GLSL shaders:
- **Vertex Shader**: Transforms vertices and calculates lighting data
- **Fragment Shader**: Implements Phong lighting with ambient, diffuse, and specular components

## Extending the Game

### Adding New Objects
```cpp
// Create a new game object
auto newObject = std::shared_ptr<GameObject>(GameObject::createCube(2.0f));
newObject->setPosition(QVector3D(5.0f, 0.0f, 0.0f));
newObject->setColor(QVector3D(1.0f, 0.0f, 0.0f)); // Red
gameEngine->addGameObject(newObject);
```

### Custom Shapes
Extend the GameObject class to create custom meshes:
```cpp
void GameObject::generateCustomVertices() {
    // Define your vertices with positions and normals
    QVector<float> vertices = { /* your vertex data */ };
    QVector<unsigned int> indices = { /* your index data */ };
    setVertices(vertices);
    setIndices(indices);
}
```

### Game Logic
Add custom update logic in GameEngine::updateGameObjects():
```cpp
void GameEngine::updateGameObjects(float deltaTime) {
    for (auto& object : m_gameObjects) {
        // Add your custom object behavior here
        // Example: physics, animations, AI, etc.
    }
}
```

## Troubleshooting

### Common Issues

1. **Black Screen**: Check OpenGL version support and shader compilation logs
2. **Build Errors**: Ensure all Qt modules are installed and properly configured
3. **Performance Issues**: Check graphics drivers and reduce scene complexity
4. **Input Not Working**: Verify focus policy and event handling

### Debug Information
The application outputs debug information to the console, including:
- OpenGL version and GLSL version
- Shader compilation status
- Initialization success messages

## Performance Tips

- Use indexed rendering for complex meshes
- Implement frustum culling for large scenes
- Consider level-of-detail (LOD) systems for distant objects
- Profile using Qt Creator's built-in profiler

## License

This project is provided as an educational example. Feel free to use and modify for your own projects.

## Contributing

This is a foundation project designed for learning and extension. Consider adding:
- Texture support
- Model loading (OBJ, FBX, etc.)
- Physics integration
- Audio system
- Scene management
- Material system
- Shadow mapping
- Post-processing effects
