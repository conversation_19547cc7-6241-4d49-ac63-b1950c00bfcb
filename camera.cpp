#include "camera.h"
#include <QtMath>

Camera::Camera()
    : m_position(0.0f, 0.0f, 3.0f)
    , m_worldUp(0.0f, 1.0f, 0.0f)
    , m_yaw(-90.0f)
    , m_pitch(0.0f)
    , m_movementSpeed(5.0f)
    , m_mouseSensitivity(0.1f)
{
    updateCameraVectors();
}

void Camera::moveForward(float distance)
{
    m_position += m_forward * distance * m_movementSpeed;
}

void Camera::moveBackward(float distance)
{
    m_position -= m_forward * distance * m_movementSpeed;
}

void Camera::moveLeft(float distance)
{
    m_position -= m_right * distance * m_movementSpeed;
}

void Camera::moveRight(float distance)
{
    m_position += m_right * distance * m_movementSpeed;
}

void Camera::moveUp(float distance)
{
    m_position += m_up * distance * m_movementSpeed;
}

void Camera::moveDown(float distance)
{
    m_position -= m_up * distance * m_movementSpeed;
}

void Camera::rotate(float yaw, float pitch)
{
    m_yaw += yaw * m_mouseSensitivity;
    m_pitch += pitch * m_mouseSensitivity;
    
    // Constrain pitch
    if (m_pitch > MAX_PITCH)
        m_pitch = MAX_PITCH;
    if (m_pitch < MIN_PITCH)
        m_pitch = MIN_PITCH;
    
    updateCameraVectors();
}

void Camera::setRotation(float yaw, float pitch)
{
    m_yaw = yaw;
    m_pitch = qBound(MIN_PITCH, pitch, MAX_PITCH);
    updateCameraVectors();
}

void Camera::setPosition(const QVector3D &position)
{
    m_position = position;
}

QMatrix4x4 Camera::viewMatrix() const
{
    QMatrix4x4 view;
    view.lookAt(m_position, m_position + m_forward, m_up);
    return view;
}

void Camera::reset()
{
    m_position = QVector3D(0.0f, 0.0f, 3.0f);
    m_yaw = -90.0f;
    m_pitch = 0.0f;
    updateCameraVectors();
}

void Camera::updateCameraVectors()
{
    // Calculate the new forward vector
    QVector3D forward;
    forward.setX(qCos(qDegreesToRadians(m_yaw)) * qCos(qDegreesToRadians(m_pitch)));
    forward.setY(qSin(qDegreesToRadians(m_pitch)));
    forward.setZ(qSin(qDegreesToRadians(m_yaw)) * qCos(qDegreesToRadians(m_pitch)));
    m_forward = forward.normalized();
    
    // Calculate right and up vectors
    m_right = QVector3D::crossProduct(m_forward, m_worldUp).normalized();
    m_up = QVector3D::crossProduct(m_right, m_forward).normalized();
}
