#ifndef OPENGLWIDGET_H
#define OPENGLWIDGET_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <QMatrix4x4>
#include <QTimer>
#include <QElapsedTimer>
#include <QMouseEvent>
#include <QKeyEvent>
#include <memory>

class GameEngine;
class Camera;

/**
 * @brief The OpenGLWidget class handles OpenGL rendering and input
 * 
 * This class manages the OpenGL context, shader programs, and
 * coordinates with the game engine for rendering and input handling.
 */
class OpenGLWidget : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    explicit OpenGLWidget(QWidget *parent = nullptr);
    ~OpenGLWidget();
    
    // Input handling
    void handleKeyPress(QKeyEvent *event);
    void handleKeyRelease(QKeyEvent *event);
    
    // Camera access
    QVector3D getCameraPosition() const;
    void resetCamera();
    
    // Performance metrics
    float getFPS() const;

protected:
    // OpenGL overrides
    void initializeGL() override;
    void resizeGL(int w, int h) override;
    void paintGL() override;
    
    // Input events
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void focusInEvent(QFocusEvent *event) override;
    void focusOutEvent(QFocusEvent *event) override;

private slots:
    void updateFrame();

private:
    void setupShaders();
    void setupMatrices();
    void renderScene();
    void renderGameObject(const std::shared_ptr<class GameObject> &object);
    
    // Game engine
    std::unique_ptr<GameEngine> m_gameEngine;
    
    // OpenGL resources
    std::unique_ptr<QOpenGLShaderProgram> m_shaderProgram;
    
    // Matrices
    QMatrix4x4 m_projectionMatrix;
    QMatrix4x4 m_viewMatrix;
    QMatrix4x4 m_modelMatrix;
    
    // Shader uniform locations
    int m_modelMatrixLocation;
    int m_viewMatrixLocation;
    int m_projectionMatrixLocation;
    int m_normalMatrixLocation;
    int m_lightPositionLocation;
    int m_lightColorLocation;
    int m_objectColorLocation;
    int m_viewPositionLocation;
    
    // Timing
    QTimer *m_updateTimer;
    QElapsedTimer m_frameTimer;
    qint64 m_lastFrameTime;
    float m_deltaTime;
    
    // Mouse handling
    bool m_mousePressed;
    QPoint m_lastMousePosition;
    bool m_mouseCaptured;
    
    // Window properties
    int m_windowWidth;
    int m_windowHeight;
    
    // Performance
    float m_fps;
    int m_frameCount;
    qint64 m_lastFPSUpdate;
};

#endif // OPENGLWIDGET_H
