#ifndef CAMERA_H
#define CAMERA_H

#include <QVector3D>
#include <QMatrix4x4>

/**
 * @brief The Camera class handles 3D camera functionality
 * 
 * This class provides a first-person style camera with position,
 * rotation, and view matrix generation capabilities.
 */
class Camera
{
public:
    Camera();
    
    // Camera movement
    void moveForward(float distance);
    void moveBackward(float distance);
    void moveLeft(float distance);
    void moveRight(float distance);
    void moveUp(float distance);
    void moveDown(float distance);
    
    // Camera rotation
    void rotate(float yaw, float pitch);
    void setRotation(float yaw, float pitch);
    
    // Position
    void setPosition(const QVector3D &position);
    QVector3D position() const { return m_position; }
    
    // View matrix
    QMatrix4x4 viewMatrix() const;
    
    // Camera vectors
    QVector3D forward() const { return m_forward; }
    QVector3D right() const { return m_right; }
    QVector3D up() const { return m_up; }
    
    // Reset camera to default position
    void reset();
    
    // Camera parameters
    void setMovementSpeed(float speed) { m_movementSpeed = speed; }
    void setSensitivity(float sensitivity) { m_mouseSensitivity = sensitivity; }
    
    float movementSpeed() const { return m_movementSpeed; }
    float sensitivity() const { return m_mouseSensitivity; }

private:
    void updateCameraVectors();
    
    // Camera position and orientation
    QVector3D m_position;
    QVector3D m_forward;
    QVector3D m_right;
    QVector3D m_up;
    QVector3D m_worldUp;
    
    // Euler angles
    float m_yaw;
    float m_pitch;
    
    // Camera options
    float m_movementSpeed;
    float m_mouseSensitivity;
    
    // Constraints
    static constexpr float MAX_PITCH = 89.0f;
    static constexpr float MIN_PITCH = -89.0f;
};

#endif // CAMERA_H
