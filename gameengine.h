#ifndef GAMEENGINE_H
#define GAMEENGINE_H

#include <QObject>
#include <QElapsedTimer>
#include <QVector>
#include <QSet>
#include <memory>

class GameObject;
class Camera;

/**
 * @brief The GameEngine class manages the game state and logic
 * 
 * This class handles the game loop, object management, timing,
 * and input processing for the 3D game.
 */
class GameEngine : public QObject
{
    Q_OBJECT

public:
    explicit GameEngine(QObject *parent = nullptr);
    ~GameEngine();
    
    // Game loop
    void update(float deltaTime);
    void render();
    
    // Object management
    void addGameObject(std::shared_ptr<GameObject> object);
    void removeGameObject(std::shared_ptr<GameObject> object);
    void clearGameObjects();
    const QVector<std::shared_ptr<GameObject>>& gameObjects() const { return m_gameObjects; }
    
    // Camera
    void setCamera(std::shared_ptr<Camera> camera);
    std::shared_ptr<Camera> camera() const { return m_camera; }
    
    // Input handling
    void handleKeyPress(int key);
    void handleKeyRelease(int key);
    void handleMouseMove(float deltaX, float deltaY);
    
    // Game state
    void start();
    void pause();
    void resume();
    void stop();
    
    bool isRunning() const { return m_running; }
    bool isPaused() const { return m_paused; }
    
    // Timing
    float deltaTime() const { return m_deltaTime; }
    float totalTime() const { return m_totalTime; }
    int frameCount() const { return m_frameCount; }
    float fps() const { return m_fps; }
    
    // Scene setup
    void setupDefaultScene();
    
    // Lighting
    struct Light {
        QVector3D position;
        QVector3D color;
        float intensity;
        
        Light(const QVector3D &pos = QVector3D(0, 5, 0),
              const QVector3D &col = QVector3D(1, 1, 1),
              float intens = 1.0f)
            : position(pos), color(col), intensity(intens) {}
    };
    
    void setLight(const Light &light) { m_light = light; }
    const Light& light() const { return m_light; }

private:
    void updateInput(float deltaTime);
    void updateGameObjects(float deltaTime);
    void calculateFPS();
    
    // Game objects
    QVector<std::shared_ptr<GameObject>> m_gameObjects;
    std::shared_ptr<Camera> m_camera;
    
    // Game state
    bool m_running;
    bool m_paused;
    
    // Timing
    QElapsedTimer m_timer;
    float m_deltaTime;
    float m_totalTime;
    int m_frameCount;
    float m_fps;
    qint64 m_lastFrameTime;
    
    // Input state
    QSet<int> m_pressedKeys;
    float m_mouseSensitivity;
    
    // Lighting
    Light m_light;
    
    // Movement speed
    float m_movementSpeed;
};

#endif // GAMEENGINE_H
