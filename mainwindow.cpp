#include "mainwindow.h"
#include "openglwidget.h"
#include <QApplication>
#include <QMenuBar>
#include <QStatusBar>
#include <QLabel>
#include <QTimer>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QWidget>
#include <QMessageBox>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_openglWidget(nullptr)
    , m_fps<PERSON>abel(nullptr)
    , m_positionLabel(nullptr)
    , m_statusUpdateTimer(nullptr)
{
    setupUI();
    setupMenuBar();
    setupStatusBar();

    // Set up status bar update timer
    m_statusUpdateTimer = new QTimer(this);
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_statusUpdateTimer->start(100); // Update every 100ms

    // Set window properties
    setWindowTitle("Simple 3D Game - Qt OpenGL");
    resize(1024, 768);
    setFocusPolicy(Qt::StrongFocus);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    // Create the central widget
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // Create the OpenGL widget
    m_openglWidget = new OpenGLWidget(this);
    
    // Set up layout
    QVBoxLayout *layout = new QVBoxLayout(centralWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_openglWidget);
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");
    
    QAction *exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // View menu
    QMenu *viewMenu = menuBar()->addMenu("&View");
    
    QAction *resetCameraAction = viewMenu->addAction("&Reset Camera");
    resetCameraAction->setShortcut(QKeySequence("R"));
    connect(resetCameraAction, &QAction::triggered, m_openglWidget, &OpenGLWidget::resetCamera);

    // Help menu
    QMenu *helpMenu = menuBar()->addMenu("&Help");
    
    QAction *aboutAction = helpMenu->addAction("&About");
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About Simple 3D Game",
                          "Simple 3D Game using Qt OpenGL\n\n"
                          "Controls:\n"
                          "WASD - Move camera\n"
                          "Mouse - Look around\n"
                          "R - Reset camera\n"
                          "ESC - Exit");
    });
}

void MainWindow::setupStatusBar()
{
    m_fpsLabel = new QLabel("FPS: 0");
    m_positionLabel = new QLabel("Position: (0, 0, 0)");
    
    statusBar()->addWidget(m_fpsLabel);
    statusBar()->addPermanentWidget(m_positionLabel);
}

void MainWindow::updateStatusBar()
{
    if (m_openglWidget) {
        // Update FPS
        float fps = m_openglWidget->getFPS();
        m_fpsLabel->setText(QString("FPS: %1").arg(fps, 0, 'f', 1));
        
        // Update camera position
        auto pos = m_openglWidget->getCameraPosition();
        m_positionLabel->setText(QString("Position: (%1, %2, %3)")
                                .arg(pos.x(), 0, 'f', 1)
                                .arg(pos.y(), 0, 'f', 1)
                                .arg(pos.z(), 0, 'f', 1));
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Escape) {
        close();
        return;
    }
    
    // Forward key events to OpenGL widget
    if (m_openglWidget) {
        m_openglWidget->handleKeyPress(event);
    }
    
    QMainWindow::keyPressEvent(event);
}

void MainWindow::keyReleaseEvent(QKeyEvent *event)
{
    // Forward key events to OpenGL widget
    if (m_openglWidget) {
        m_openglWidget->handleKeyRelease(event);
    }
    
    QMainWindow::keyReleaseEvent(event);
}
